#!/usr/bin/env python3
"""
脚本用于修改JSON文件，为每个条目添加label字段
将 "./imagesTs/liver_XXX.nii.gz" 对应的label设置为 "./pseudoLabel/liver_XXX.nii.gz"
"""

import json
import os

def modify_json_file(input_file, output_file):
    """
    修改JSON文件，为每个条目添加label字段
    
    Args:
        input_file (str): 输入JSON文件路径
        output_file (str): 输出JSON文件路径
    """
    try:
        # 读取原始JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 修改validation数组中的每个条目
        if 'validation' in data:
            for item in data['validation']:
                if 'image' in item:
                    # 获取image路径
                    image_path = item['image']
                    # 将imagesTs替换为pseudoLabel来生成label路径
                    label_path = image_path.replace('./imagesTs/', './pseudoLabel/')
                    # 添加label字段
                    item['label'] = label_path
        
        # 将修改后的数据写入新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"成功修改JSON文件！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"共处理了 {len(data.get('validation', []))} 个条目")
        
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
    except json.JSONDecodeError:
        print(f"错误: {input_file} 不是有效的JSON文件")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    # 设置输入和输出文件路径
    input_file = "/home/<USER>/data/tumor/Dataset017_Liver/sss.json"
    output_file = "/home/<USER>/data/tumor/Dataset017_Liver/sss1.json"
    
    # 执行修改
    modify_json_file(input_file, output_file)
