#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply SLIC superpixel segmentation to images
- Reads images from /home/<USER>/data/ACDC/data/imgs
- Saves superpixel labels to /home/<USER>/data/ACDC/data/slic
- Saves visualization images to /home/<USER>/data/ACDC/data/img_slic
"""

import os
import glob
import numpy as np
from PIL import Image
import cv2
from skimage.segmentation import slic, mark_boundaries
from skimage import img_as_float
import matplotlib.pyplot as plt

def apply_slic_superpixels(image_path, n_segments=100, compactness=10, sigma=1):
    """
    Apply SLIC superpixel segmentation to an image
    
    Args:
        image_path: Path to input image
        n_segments: Number of superpixels (approximate)
        compactness: Balance between color proximity and space proximity
        sigma: Gaussian smoothing parameter
    
    Returns:
        segments: Superpixel labels
        image: Original image as numpy array
    """
    # Read image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Convert to float for SLIC
    image_float = img_as_float(image)
    
    # Apply SLIC
    segments = slic(image_float, n_segments=n_segments, compactness=compactness, 
                   sigma=sigma, start_label=1)
    
    return segments, image

def save_superpixel_labels(segments, output_path):
    """Save superpixel labels as image"""
    # Normalize labels to 0-255 range for visualization
    labels_normalized = ((segments - segments.min()) / 
                        (segments.max() - segments.min()) * 255).astype(np.uint8)
    
    # Save as PNG
    Image.fromarray(labels_normalized).save(output_path)
    
    # Also save raw labels as numpy array for potential future use
    np_output_path = output_path.replace('.png', '.npy')
    np.save(np_output_path, segments)

def create_superpixel_visualization(image, segments, output_path):
    """Create and save visualization with superpixel boundaries"""
    # Mark boundaries
    marked = mark_boundaries(image, segments, color=(1, 1, 1), mode='inner')
    
    # Convert to 0-255 range
    marked_uint8 = (marked * 255).astype(np.uint8)
    
    # Save visualization
    Image.fromarray(marked_uint8).save(output_path)

def process_single_image(image_path, slic_dir, img_slic_dir, 
                        n_segments=100, compactness=10, sigma=1):
    """Process a single image with SLIC superpixels"""
    try:
        # Get base filename
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        print(f"Processing: {base_name}")
        
        # Apply SLIC
        segments, image = apply_slic_superpixels(image_path, n_segments, compactness, sigma)
        
        # Save superpixel labels
        slic_output_path = os.path.join(slic_dir, f"{base_name}_slic.png")
        save_superpixel_labels(segments, slic_output_path)
        
        # Save visualization
        viz_output_path = os.path.join(img_slic_dir, f"{base_name}_slic_viz.png")
        create_superpixel_visualization(image, segments, viz_output_path)
        
        print(f"  - Saved labels: {slic_output_path}")
        print(f"  - Saved visualization: {viz_output_path}")
        print(f"  - Number of superpixels: {len(np.unique(segments))}")
        
        return True
        
    except Exception as e:
        print(f"Error processing {image_path}: {str(e)}")
        return False

def main():
    # Define paths
    imgs_dir = "/home/<USER>/data/ACDC/data/imgs"
    slic_dir = "/home/<USER>/data/ACDC/data/slic"
    img_slic_dir = "/home/<USER>/data/ACDC/data/img_slic"
    
    # Create output directories
    os.makedirs(slic_dir, exist_ok=True)
    os.makedirs(img_slic_dir, exist_ok=True)
    
    # Get all image files (only process _image.png files, not _label.png)
    image_pattern = os.path.join(imgs_dir, "*_image.png")
    image_files = glob.glob(image_pattern)
    
    print(f"Found {len(image_files)} image files to process")
    print(f"Output directories:")
    print(f"  - SLIC labels: {slic_dir}")
    print(f"  - Visualizations: {img_slic_dir}")
    
    # SLIC parameters
    n_segments = 50  # Approximate number of superpixels
    compactness = 10  # Balance between color and spatial proximity
    sigma = 1         # Gaussian smoothing
    
    print(f"\nSLIC parameters:")
    print(f"  - n_segments: {n_segments}")
    print(f"  - compactness: {compactness}")
    print(f"  - sigma: {sigma}")
    
    # Process each image
    successful_count = 0
    failed_count = 0
    
    for i, image_path in enumerate(image_files):
        print(f"\nProcessing {i+1}/{len(image_files)}")
        
        if process_single_image(image_path, slic_dir, img_slic_dir, 
                               n_segments, compactness, sigma):
            successful_count += 1
        else:
            failed_count += 1
        
        # Print progress every 50 files
        if (i + 1) % 50 == 0:
            print(f"Progress: {i+1}/{len(image_files)} files processed")
    
    print(f"\nProcessing complete!")
    print(f"Successfully processed: {successful_count}")
    print(f"Failed: {failed_count}")
    print(f"SLIC labels saved to: {slic_dir}")
    print(f"Visualizations saved to: {img_slic_dir}")

if __name__ == "__main__":
    main()
